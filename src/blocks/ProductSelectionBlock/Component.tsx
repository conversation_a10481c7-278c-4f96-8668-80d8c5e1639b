'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ShoppingCart, Package, Shirt, Palette } from 'lucide-react'

import { DTFStickersTab } from './DTFStickersTab'
import { SolidTshirtsTab } from './SolidTshirtsTab'
import { CustomTshirtsTab } from './CustomTshirtsTab'

// Define types for the component props
type ProductSelectionBlockProps = {
  blockType: 'product-selection-block'
  heading?: string
  subheading?: string
  enableDTFStickers?: boolean
  enableSolidTshirts?: boolean
  enableCustomTshirts?: boolean
  defaultTab?: 'dtf-stickers' | 'solid-tshirts' | 'custom-tshirts'
  backgroundColor?: string
  textColor?: string
  padding?: string
  disableInnerContainer?: boolean
}

type ServiceTab = 'dtf-stickers' | 'solid-tshirts' | 'custom-tshirts'

interface CartItem {
  id: string
  type: ServiceTab
  data: any
  summary: string
  price: number
}

export const ProductSelectionBlock: React.FC<ProductSelectionBlockProps> = ({
  heading = 'Select Your Service',
  subheading = 'Choose from our three specialized printing services',
  enableDTFStickers = true,
  enableSolidTshirts = true,
  enableCustomTshirts = true,
  defaultTab = 'dtf-stickers',
  backgroundColor,
  textColor,
  padding = 'py-16',
  disableInnerContainer = false,
}) => {
  const [activeTab, setActiveTab] = useState<ServiceTab>(defaultTab)
  const [cartItems, setCartItems] = useState<CartItem[]>([])

  const tabs = [
    {
      id: 'dtf-stickers' as ServiceTab,
      label: 'DTF Stickers',
      icon: Package,
      description: '100% upfront payment',
      enabled: enableDTFStickers,
      color: 'bg-blue-500',
    },
    {
      id: 'solid-tshirts' as ServiceTab,
      label: 'Solid T-shirts',
      icon: Shirt,
      description: '100% upfront payment',
      enabled: enableSolidTshirts,
      color: 'bg-green-500',
    },
    {
      id: 'custom-tshirts' as ServiceTab,
      label: 'Custom T-shirts',
      icon: Palette,
      description: '50% advance, 50% on delivery',
      enabled: enableCustomTshirts,
      color: 'bg-purple-500',
    },
  ].filter((tab) => tab.enabled)

  const handleAddToCart = (type: ServiceTab, data: any) => {
    let summary = ''
    let price = 0

    switch (type) {
      case 'dtf-stickers':
        summary = `DTF Stickers - ${data.quantity}m, ${data.material}, ${data.finish}`
        price = data.totalPrice
        break
      case 'solid-tshirts':
        summary = `${data.length} T-shirt${data.length > 1 ? 's' : ''}`
        price = data.reduce((total: number, item: any) => total + item.totalPrice, 0)
        break
      case 'custom-tshirts':
        summary = `Custom T-shirt - ${data.quantity} pcs, ${data.printPlacement} print`
        price = data.advanceAmount // For custom t-shirts, we show advance amount
        break
    }

    const newItem: CartItem = {
      id: `${type}-${Date.now()}`,
      type,
      data,
      summary,
      price,
    }

    setCartItems((prev) => [...prev, newItem])

    // Show success message or redirect to cart
    console.log('Added to cart:', newItem)
  }

  const removeFromCart = (itemId: string) => {
    setCartItems((prev) => prev.filter((item) => item.id !== itemId))
  }

  const getTotalCartValue = () => {
    return cartItems.reduce((total, item) => total + item.price, 0)
  }

  const containerClasses = disableInnerContainer ? '' : 'container mx-auto px-4'
  const sectionStyle = {
    backgroundColor,
    color: textColor,
  }

  return (
    <section className={padding} style={sectionStyle}>
      <div className={containerClasses}>
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{heading}</h2>
          {subheading && <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subheading}</p>}
        </div>

        {/* Service Tabs */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Tab Navigation */}
          <div className="lg:w-80">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Services
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <Button
                      key={tab.id}
                      variant={activeTab === tab.id ? 'default' : 'outline'}
                      className="w-full justify-start h-auto p-4"
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-10 h-10 rounded-lg ${tab.color} flex items-center justify-center`}
                        >
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="text-left">
                          <div className="font-semibold">{tab.label}</div>
                          <div className="text-xs text-gray-500">{tab.description}</div>
                        </div>
                      </div>
                    </Button>
                  )
                })}
              </CardContent>
            </Card>

            {/* Cart Summary */}
            {cartItems.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    Cart ({cartItems.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {cartItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex justify-between items-start p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex-1">
                          <Badge variant="outline" className="mb-1">
                            {item.type.replace('-', ' ')}
                          </Badge>
                          <p className="text-sm font-medium">{item.summary}</p>
                          <p className="text-sm text-gray-600">₹{item.price.toLocaleString()}</p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFromCart(item.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>

                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between items-center font-semibold">
                      <span>Total:</span>
                      <span>₹{getTotalCartValue().toLocaleString()}</span>
                    </div>
                    <Button className="w-full mt-3">Proceed to Checkout</Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Tab Content */}
          <div className="flex-1">
            <Card>
              <CardContent className="p-6">
                {activeTab === 'dtf-stickers' && enableDTFStickers && (
                  <DTFStickersTab onAddToCart={(data) => handleAddToCart('dtf-stickers', data)} />
                )}
                {activeTab === 'solid-tshirts' && enableSolidTshirts && (
                  <SolidTshirtsTab onAddToCart={(data) => handleAddToCart('solid-tshirts', data)} />
                )}
                {activeTab === 'custom-tshirts' && enableCustomTshirts && (
                  <CustomTshirtsTab
                    onAddToCart={(data) => handleAddToCart('custom-tshirts', data)}
                  />
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
