'use client'

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Upload, X, ZoomIn, ZoomOut } from 'lucide-react'

interface DTFStickersTabProps {
  onAddToCart: (data: DTFStickerData) => void
}

interface DTFStickerData {
  designFile: File | null
  uploadedFileUrl?: string
  uploadedFileKey?: string
  dimensions: {
    width: number
    height: number
    isCustomSize: boolean
  }
  material: string
  finish: string
  quantity: number
  pricePerUnit: number
  totalPrice: number
}

const PRESET_SIZES = [
  { label: '5cm x 5cm', width: 5, height: 5 },
  { label: '10cm x 10cm', width: 10, height: 10 },
  { label: '15cm x 15cm', width: 15, height: 15 },
  { label: '20cm x 20cm', width: 20, height: 20 },
  { label: 'Custom Size', width: 0, height: 0, isCustom: true },
]

const MATERIALS = [
  { value: 'matte', label: 'Matte' },
  { value: 'glossy', label: 'Glossy' },
  { value: 'transparent', label: 'Transparent' },
  { value: 'holographic', label: 'Holographic' },
]

const FINISHES = [
  { value: 'standard', label: 'Standard' },
  { value: 'laminated', label: 'Laminated' },
  { value: 'uv-resistant', label: 'UV Resistant' },
]

export const DTFStickersTab: React.FC<DTFStickersTabProps> = ({ onAddToCart }) => {
  const [formData, setFormData] = useState<DTFStickerData>({
    designFile: null,
    dimensions: { width: 0, height: 0, isCustomSize: false },
    material: '',
    finish: '',
    quantity: 1,
    pricePerUnit: 0,
    totalPrice: 0,
  })

  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [zoom, setZoom] = useState(1)

  // Calculate price based on quantity tiers
  const calculatePrice = (quantity: number): number => {
    if (quantity <= 5) return 250
    if (quantity <= 10) return 240
    return 220
  }

  // Update total price when quantity changes
  const updatePricing = (newQuantity: number) => {
    const pricePerUnit = calculatePrice(newQuantity)
    const totalPrice = pricePerUnit * newQuantity

    setFormData((prev) => ({
      ...prev,
      quantity: newQuantity,
      pricePerUnit,
      totalPrice,
    }))
  }

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setFormData((prev) => ({ ...prev, designFile: file }))

      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)

      // Upload to S3 (or local storage)
      try {
        const uploadFormData = new FormData()
        uploadFormData.append('file', file)
        uploadFormData.append('folder', 'dtf-designs')

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: uploadFormData,
        })

        if (response.ok) {
          const result = await response.json()
          console.log('File uploaded successfully:', result)
          // Store the uploaded file URL for later use
          setFormData((prev) => ({
            ...prev,
            uploadedFileUrl: result.url,
            uploadedFileKey: result.key,
          }))
        } else {
          console.error('Upload failed:', await response.text())
        }
      } catch (error) {
        console.error('Upload error:', error)
      }
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.svg'],
      'application/pdf': ['.pdf'],
    },
    maxFiles: 1,
  })

  const removeFile = () => {
    setFormData((prev) => ({ ...prev, designFile: null }))
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
      setPreviewUrl(null)
    }
  }

  const handlePresetSizeSelect = (preset: (typeof PRESET_SIZES)[0]) => {
    if (preset.isCustom) {
      setFormData((prev) => ({
        ...prev,
        dimensions: { ...prev.dimensions, isCustomSize: true },
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        dimensions: {
          width: preset.width,
          height: preset.height,
          isCustomSize: false,
        },
      }))
    }
  }

  const isFormValid = () => {
    return (
      formData.designFile &&
      formData.dimensions.width > 0 &&
      formData.dimensions.height > 0 &&
      formData.material &&
      formData.finish &&
      formData.quantity > 0
    )
  }

  const handleAddToCart = () => {
    if (isFormValid()) {
      onAddToCart(formData)
    }
  }

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Design File</CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-primary'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {isDragActive ? (
              <p>Drop the file here...</p>
            ) : (
              <div>
                <p className="text-lg font-medium">Drag & drop your design file here</p>
                <p className="text-sm text-gray-500 mt-2">Supports PNG, JPG, SVG, PDF (Max 10MB)</p>
                <Button variant="outline" className="mt-4">
                  Browse Files
                </Button>
              </div>
            )}
          </div>

          {formData.designFile && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded flex items-center justify-center">
                    <Upload className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{formData.designFile.name}</p>
                    <p className="text-sm text-gray-500">
                      {(formData.designFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={removeFile}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Design Preview */}
      {previewUrl && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Design Preview
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="text-sm">{Math.round(zoom * 100)}%</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoom(Math.min(3, zoom + 0.25))}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-gray-50 overflow-auto max-h-96">
              <img
                src={previewUrl}
                alt="Design preview"
                style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}
                className="max-w-full h-auto"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dimensions Section */}
      <Card>
        <CardHeader>
          <CardTitle>Sticker Dimensions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {PRESET_SIZES.map((preset, index) => (
              <Button
                key={index}
                variant={
                  !formData.dimensions.isCustomSize &&
                  formData.dimensions.width === preset.width &&
                  formData.dimensions.height === preset.height
                    ? 'default'
                    : 'outline'
                }
                onClick={() => handlePresetSizeSelect(preset)}
                className="h-auto py-3"
              >
                {preset.label}
              </Button>
            ))}
          </div>

          {formData.dimensions.isCustomSize && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <Label htmlFor="width">Width (cm)</Label>
                <Input
                  id="width"
                  type="number"
                  min="1"
                  value={formData.dimensions.width || ''}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      dimensions: {
                        ...prev.dimensions,
                        width: parseFloat(e.target.value) || 0,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <Label htmlFor="height">Height (cm)</Label>
                <Input
                  id="height"
                  type="number"
                  min="1"
                  value={formData.dimensions.height || ''}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      dimensions: {
                        ...prev.dimensions,
                        height: parseFloat(e.target.value) || 0,
                      },
                    }))
                  }
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Material and Finish */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Material</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={formData.material}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, material: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select material" />
              </SelectTrigger>
              <SelectContent>
                {MATERIALS.map((material) => (
                  <SelectItem key={material.value} value={material.value}>
                    {material.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Finish</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={formData.finish}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, finish: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select finish" />
              </SelectTrigger>
              <SelectContent>
                {FINISHES.map((finish) => (
                  <SelectItem key={finish.value} value={finish.value}>
                    {finish.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </div>

      {/* Quantity and Pricing */}
      <Card>
        <CardHeader>
          <CardTitle>Quantity & Pricing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="quantity">Quantity (meters)</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) => updatePricing(parseInt(e.target.value) || 1)}
            />
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-600 mb-2">Pricing Tiers:</div>
            <div className="text-xs space-y-1">
              <div>0-5 meters: ₹250 per meter</div>
              <div>5-10 meters: ₹240 per meter</div>
              <div>10+ meters: ₹220 per meter</div>
            </div>
          </div>

          {formData.quantity > 0 && (
            <div className="border-t pt-4">
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total Price:</span>
                <span>₹{formData.totalPrice.toLocaleString()}</span>
              </div>
              <div className="text-sm text-gray-600">
                ₹{formData.pricePerUnit} × {formData.quantity} meters
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add to Cart Button */}
      <Button onClick={handleAddToCart} disabled={!isFormValid()} className="w-full h-12 text-lg">
        Add DTF Stickers to Cart
      </Button>
    </div>
  )
}
