'use client'

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Upload, X, ZoomIn, ZoomOut, Shirt, Palette, Ruler } from 'lucide-react'

interface CustomTshirtsTabProps {
  onAddToCart: (data: CustomTshirtData) => void
}

interface CustomTshirtData {
  tshirtSelection: {
    baseProduct: string
    size: string
    color: string
    material: string
    style: string
  }
  designFile: File | null
  mockupImage?: string
  printPlacement: string
  printSize: {
    width: number
    height: number
  }
  quantity: number
  advanceAmount: number
  remainingAmount: number
  totalPrice: number
}

interface BaseTshirt {
  id: string
  name: string
  basePrice: number
  image: string
  colors: string[]
  materials: string[]
  styles: string[]
}

// Mock base t-shirt options
const BASE_TSHIRTS: BaseTshirt[] = [
  {
    id: '1',
    name: 'Premium Cotton Tee',
    basePrice: 299,
    image: '/api/placeholder/300/300',
    colors: ['White', 'Black', 'Navy', 'Red', 'Gray'],
    materials: ['Cotton'],
    styles: ['Crew Neck', 'V-Neck'],
  },
  {
    id: '2',
    name: 'Poly-Cotton Blend',
    basePrice: 199,
    image: '/api/placeholder/300/300',
    colors: ['White', 'Black', 'Blue', 'Green', 'Yellow'],
    materials: ['Poly-Cotton Blend'],
    styles: ['Crew Neck', 'Polo'],
  },
]

const SIZES = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL']
const PRINT_PLACEMENTS = [
  { value: 'front', label: 'Front', icon: '👕' },
  { value: 'back', label: 'Back', icon: '🔄' },
  { value: 'left-sleeve', label: 'Left Sleeve', icon: '👈' },
  { value: 'right-sleeve', label: 'Right Sleeve', icon: '👉' },
  { value: 'front-back', label: 'Front & Back', icon: '🔄👕' },
]

const PRINT_SIZE_PRESETS = [
  { label: 'Small (10x10 cm)', width: 10, height: 10 },
  { label: 'Medium (15x15 cm)', width: 15, height: 15 },
  { label: 'Large (20x20 cm)', width: 20, height: 20 },
  { label: 'Extra Large (25x25 cm)', width: 25, height: 25 },
  { label: 'Custom Size', width: 0, height: 0, isCustom: true },
]

export const CustomTshirtsTab: React.FC<CustomTshirtsTabProps> = ({ onAddToCart }) => {
  const [currentStep, setCurrentStep] = useState<'tshirt' | 'design'>('tshirt')
  const [formData, setFormData] = useState<CustomTshirtData>({
    tshirtSelection: {
      baseProduct: '',
      size: '',
      color: '',
      material: '',
      style: '',
    },
    designFile: null,
    printPlacement: '',
    printSize: { width: 0, height: 0 },
    quantity: 1,
    advanceAmount: 0,
    remainingAmount: 0,
    totalPrice: 0,
  })
  
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [mockupUrl, setMockupUrl] = useState<string | null>(null)
  const [zoom, setZoom] = useState(1)
  const [isCustomPrintSize, setIsCustomPrintSize] = useState(false)

  const selectedTshirt = BASE_TSHIRTS.find(t => t.id === formData.tshirtSelection.baseProduct)

  // Calculate pricing
  const calculatePricing = () => {
    if (!selectedTshirt) return

    const basePrice = selectedTshirt.basePrice
    const printingCost = 150 // Base printing cost
    const totalPerUnit = basePrice + printingCost
    const totalPrice = totalPerUnit * formData.quantity
    const advanceAmount = Math.round(totalPrice * 0.5) // 50% advance
    const remainingAmount = totalPrice - advanceAmount

    setFormData(prev => ({
      ...prev,
      advanceAmount,
      remainingAmount,
      totalPrice,
    }))
  }

  React.useEffect(() => {
    calculatePricing()
  }, [formData.quantity, formData.tshirtSelection.baseProduct])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setFormData(prev => ({ ...prev, designFile: file }))
      
      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      
      // Generate mockup (in real implementation, this would call an API)
      generateMockup(file)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.svg'],
      'application/pdf': ['.pdf'],
    },
    maxFiles: 1,
  })

  const generateMockup = async (file: File) => {
    // Mock mockup generation - in real implementation, this would call an API
    // that overlays the design on the selected t-shirt
    setTimeout(() => {
      setMockupUrl('/api/placeholder/400/400') // Mock mockup URL
    }, 1000)
  }

  const removeFile = () => {
    setFormData(prev => ({ ...prev, designFile: null }))
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
      setPreviewUrl(null)
    }
    setMockupUrl(null)
  }

  const handleTshirtSelect = (tshirtId: string) => {
    setFormData(prev => ({
      ...prev,
      tshirtSelection: {
        ...prev.tshirtSelection,
        baseProduct: tshirtId,
        color: '',
        material: '',
        style: '',
      },
    }))
  }

  const handlePrintSizePreset = (preset: typeof PRINT_SIZE_PRESETS[0]) => {
    if (preset.isCustom) {
      setIsCustomPrintSize(true)
    } else {
      setIsCustomPrintSize(false)
      setFormData(prev => ({
        ...prev,
        printSize: { width: preset.width, height: preset.height },
      }))
    }
  }

  const isTshirtStepValid = () => {
    const { baseProduct, size, color, material, style } = formData.tshirtSelection
    return baseProduct && size && color && material && style
  }

  const isDesignStepValid = () => {
    return (
      formData.designFile &&
      formData.printPlacement &&
      formData.printSize.width > 0 &&
      formData.printSize.height > 0 &&
      formData.quantity > 0
    )
  }

  const handleAddToCart = () => {
    if (isTshirtStepValid() && isDesignStepValid()) {
      onAddToCart(formData)
    }
  }

  const TshirtSelectionStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shirt className="mx-auto h-12 w-12 text-primary mb-4" />
        <h3 className="text-xl font-semibold">Select Your T-shirt</h3>
        <p className="text-gray-600">Choose the base t-shirt for your custom design</p>
      </div>

      {/* T-shirt Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {BASE_TSHIRTS.map(tshirt => (
          <Card
            key={tshirt.id}
            className={`cursor-pointer transition-all ${
              formData.tshirtSelection.baseProduct === tshirt.id
                ? 'ring-2 ring-primary'
                : 'hover:shadow-md'
            }`}
            onClick={() => handleTshirtSelect(tshirt.id)}
          >
            <CardContent className="p-4">
              <div className="aspect-square mb-4 overflow-hidden rounded-lg">
                <img
                  src={tshirt.image}
                  alt={tshirt.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <h4 className="font-semibold text-lg">{tshirt.name}</h4>
              <p className="text-primary font-bold">₹{tshirt.basePrice}</p>
              <div className="mt-2 space-y-1">
                <p className="text-sm text-gray-600">
                  Materials: {tshirt.materials.join(', ')}
                </p>
                <p className="text-sm text-gray-600">
                  Styles: {tshirt.styles.join(', ')}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* T-shirt Customization */}
      {selectedTshirt && (
        <Card>
          <CardHeader>
            <CardTitle>Customize Your {selectedTshirt.name}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Size Selection */}
              <div>
                <Label>Size</Label>
                <Select
                  value={formData.tshirtSelection.size}
                  onValueChange={(value) =>
                    setFormData(prev => ({
                      ...prev,
                      tshirtSelection: { ...prev.tshirtSelection, size: value },
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent>
                    {SIZES.map(size => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Color Selection */}
              <div>
                <Label>Color</Label>
                <Select
                  value={formData.tshirtSelection.color}
                  onValueChange={(value) =>
                    setFormData(prev => ({
                      ...prev,
                      tshirtSelection: { ...prev.tshirtSelection, color: value },
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select color" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedTshirt.colors.map(color => (
                      <SelectItem key={color} value={color}>
                        {color}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Material Selection */}
              <div>
                <Label>Material</Label>
                <Select
                  value={formData.tshirtSelection.material}
                  onValueChange={(value) =>
                    setFormData(prev => ({
                      ...prev,
                      tshirtSelection: { ...prev.tshirtSelection, material: value },
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select material" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedTshirt.materials.map(material => (
                      <SelectItem key={material} value={material}>
                        {material}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Style Selection */}
              <div>
                <Label>Style</Label>
                <Select
                  value={formData.tshirtSelection.style}
                  onValueChange={(value) =>
                    setFormData(prev => ({
                      ...prev,
                      tshirtSelection: { ...prev.tshirtSelection, style: value },
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select style" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedTshirt.styles.map(style => (
                      <SelectItem key={style} value={style}>
                        {style}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Button
        onClick={() => setCurrentStep('design')}
        disabled={!isTshirtStepValid()}
        className="w-full h-12 text-lg"
      >
        Next: Upload Design
      </Button>
    </div>
  )

  const DesignUploadStep = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="text-center flex-1">
          <Palette className="mx-auto h-12 w-12 text-primary mb-4" />
          <h3 className="text-xl font-semibold">Upload Your Design</h3>
          <p className="text-gray-600">Add your custom design and configure printing</p>
        </div>
        <Button variant="outline" onClick={() => setCurrentStep('tshirt')}>
          Back to T-shirt Selection
        </Button>
      </div>

      {/* Selected T-shirt Summary */}
      {selectedTshirt && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <img
                src={selectedTshirt.image}
                alt={selectedTshirt.name}
                className="w-16 h-16 object-cover rounded"
              />
              <div>
                <h4 className="font-semibold">{selectedTshirt.name}</h4>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline">{formData.tshirtSelection.size}</Badge>
                  <Badge variant="outline">{formData.tshirtSelection.color}</Badge>
                  <Badge variant="outline">{formData.tshirtSelection.style}</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Design File</CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-primary'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {isDragActive ? (
              <p>Drop the design file here...</p>
            ) : (
              <div>
                <p className="text-lg font-medium">Drag & drop your design file here</p>
                <p className="text-sm text-gray-500 mt-2">
                  Supports PNG, JPG, SVG, PDF (Max 10MB)
                </p>
                <Button variant="outline" className="mt-4">
                  Browse Files
                </Button>
              </div>
            )}
          </div>

          {formData.designFile && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded flex items-center justify-center">
                    <Upload className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{formData.designFile.name}</p>
                    <p className="text-sm text-gray-500">
                      {(formData.designFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={removeFile}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Design Preview and Mockup */}
      {previewUrl && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Original Design
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm">{Math.round(zoom * 100)}%</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setZoom(Math.min(3, zoom + 0.25))}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-gray-50 overflow-auto max-h-64">
                <img
                  src={previewUrl}
                  alt="Design preview"
                  style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}
                  className="max-w-full h-auto"
                />
              </div>
            </CardContent>
          </Card>

          {mockupUrl && (
            <Card>
              <CardHeader>
                <CardTitle>Mockup Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-gray-50">
                  <img
                    src={mockupUrl}
                    alt="T-shirt mockup"
                    className="w-full h-auto max-h-64 object-contain"
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Print Configuration */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Print Placement</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {PRINT_PLACEMENTS.map(placement => (
                <Button
                  key={placement.value}
                  variant={formData.printPlacement === placement.value ? 'default' : 'outline'}
                  onClick={() =>
                    setFormData(prev => ({ ...prev, printPlacement: placement.value }))
                  }
                  className="h-auto py-3 flex flex-col items-center"
                >
                  <span className="text-lg mb-1">{placement.icon}</span>
                  <span className="text-sm">{placement.label}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ruler className="h-5 w-5" />
              Print Size
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              {PRINT_SIZE_PRESETS.map((preset, index) => (
                <Button
                  key={index}
                  variant={
                    !isCustomPrintSize &&
                    formData.printSize.width === preset.width &&
                    formData.printSize.height === preset.height
                      ? 'default'
                      : 'outline'
                  }
                  onClick={() => handlePrintSizePreset(preset)}
                  className="h-auto py-2 text-xs"
                >
                  {preset.label}
                </Button>
              ))}
            </div>

            {isCustomPrintSize && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="print-width">Width (cm)</Label>
                  <Input
                    id="print-width"
                    type="number"
                    min="1"
                    max="30"
                    value={formData.printSize.width || ''}
                    onChange={(e) =>
                      setFormData(prev => ({
                        ...prev,
                        printSize: {
                          ...prev.printSize,
                          width: parseFloat(e.target.value) || 0,
                        },
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="print-height">Height (cm)</Label>
                  <Input
                    id="print-height"
                    type="number"
                    min="1"
                    max="30"
                    value={formData.printSize.height || ''}
                    onChange={(e) =>
                      setFormData(prev => ({
                        ...prev,
                        printSize: {
                          ...prev.printSize,
                          height: parseFloat(e.target.value) || 0,
                        },
                      }))
                    }
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quantity and Pricing */}
      <Card>
        <CardHeader>
          <CardTitle>Quantity & Pricing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) =>
                setFormData(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))
              }
            />
          </div>

          {formData.totalPrice > 0 && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span>Base T-shirt (₹{selectedTshirt?.basePrice} × {formData.quantity}):</span>
                <span>₹{((selectedTshirt?.basePrice || 0) * formData.quantity).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Printing Cost (₹150 × {formData.quantity}):</span>
                <span>₹{(150 * formData.quantity).toLocaleString()}</span>
              </div>
              <div className="border-t pt-2 font-semibold">
                <div className="flex justify-between">
                  <span>Total:</span>
                  <span>₹{formData.totalPrice.toLocaleString()}</span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">Payment Terms (50/50 Split)</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Advance Payment (50%):</span>
                    <span className="font-medium">₹{formData.advanceAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>On Delivery (50%):</span>
                    <span className="font-medium">₹{formData.remainingAmount.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Button
        onClick={handleAddToCart}
        disabled={!isDesignStepValid()}
        className="w-full h-12 text-lg"
      >
        Add Custom T-shirt to Cart (Pay ₹{formData.advanceAmount.toLocaleString()} Advance)
      </Button>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Step Indicator */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        <div className={`flex items-center ${currentStep === 'tshirt' ? 'text-primary' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
            currentStep === 'tshirt' ? 'border-primary bg-primary text-white' : 'border-gray-300'
          }`}>
            1
          </div>
          <span className="ml-2 font-medium">T-shirt Selection</span>
        </div>
        <div className="w-8 h-px bg-gray-300"></div>
        <div className={`flex items-center ${currentStep === 'design' ? 'text-primary' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
            currentStep === 'design' ? 'border-primary bg-primary text-white' : 'border-gray-300'
          }`}>
            2
          </div>
          <span className="ml-2 font-medium">Design Upload</span>
        </div>
      </div>

      {currentStep === 'tshirt' ? <TshirtSelectionStep /> : <DesignUploadStep />}
    </div>
  )
}
