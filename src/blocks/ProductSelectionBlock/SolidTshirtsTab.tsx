'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Filter, Grid, List, Plus, Minus, ShoppingCart } from 'lucide-react'

interface SolidTshirtsTabProps {
  onAddToCart: (items: SolidTshirtItem[]) => void
}

interface SolidTshirtItem {
  id: string
  productId: string
  title: string
  size: string
  color: string
  material: string
  brand: string
  quantity: number
  unitPrice: number
  totalPrice: number
  image?: string
}

interface Product {
  id: string
  title: string
  description: string
  basePrice: number
  salePrice?: number
  image: string
  sizes: string[]
  colors: string[]
  materials: string[]
  brand: string
  inStock: boolean
  category: string
}

// Mock data - in real implementation, this would come from API
const MOCK_PRODUCTS: Product[] = [
  {
    id: '1',
    title: 'Premium Cotton T-Shirt',
    description: 'High-quality 100% cotton t-shirt with comfortable fit',
    basePrice: 299,
    salePrice: 249,
    image: '/api/placeholder/300/300',
    sizes: ['S', 'M', 'L', 'XL', 'XXL'],
    colors: ['White', 'Black', 'Navy', 'Red', 'Gray'],
    materials: ['Cotton'],
    brand: 'Maddox',
    inStock: true,
    category: 'Basic',
  },
  {
    id: '2',
    title: 'Poly-Cotton Blend Tee',
    description: 'Durable poly-cotton blend for everyday wear',
    basePrice: 199,
    image: '/api/placeholder/300/300',
    sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
    colors: ['White', 'Black', 'Blue', 'Green', 'Yellow'],
    materials: ['Poly-Cotton Blend'],
    brand: 'Comfort',
    inStock: true,
    category: 'Basic',
  },
  // Add more mock products as needed
]

const SIZES = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL']
const COLORS = [
  'White', 'Black', 'Navy', 'Red', 'Gray', 'Blue', 'Green', 'Yellow',
  'Orange', 'Purple', 'Pink', 'Brown', 'Maroon', 'Olive', 'Teal',
  'Lime', 'Cyan', 'Magenta', 'Silver', 'Gold', 'Beige'
]
const MATERIALS = ['Cotton', 'Polyester', 'Poly-Cotton Blend', 'Organic Cotton']
const BRANDS = ['Maddox', 'Comfort', 'Premium', 'Basic']

export const SolidTshirtsTab: React.FC<SolidTshirtsTabProps> = ({ onAddToCart }) => {
  const [products, setProducts] = useState<Product[]>(MOCK_PRODUCTS)
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(MOCK_PRODUCTS)
  const [cartItems, setCartItems] = useState<SolidTshirtItem[]>([])
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  
  // Filter states
  const [filters, setFilters] = useState({
    sizes: [] as string[],
    colors: [] as string[],
    materials: [] as string[],
    brands: [] as string[],
    priceRange: { min: 0, max: 1000 },
    inStockOnly: false,
  })
  
  const [showFilters, setShowFilters] = useState(false)

  // Apply filters
  useEffect(() => {
    let filtered = products.filter(product => {
      // Size filter
      if (filters.sizes.length > 0) {
        const hasMatchingSize = filters.sizes.some(size => product.sizes.includes(size))
        if (!hasMatchingSize) return false
      }
      
      // Color filter
      if (filters.colors.length > 0) {
        const hasMatchingColor = filters.colors.some(color => product.colors.includes(color))
        if (!hasMatchingColor) return false
      }
      
      // Material filter
      if (filters.materials.length > 0) {
        const hasMatchingMaterial = filters.materials.some(material => product.materials.includes(material))
        if (!hasMatchingMaterial) return false
      }
      
      // Brand filter
      if (filters.brands.length > 0 && !filters.brands.includes(product.brand)) {
        return false
      }
      
      // Price filter
      const price = product.salePrice || product.basePrice
      if (price < filters.priceRange.min || price > filters.priceRange.max) {
        return false
      }
      
      // Stock filter
      if (filters.inStockOnly && !product.inStock) {
        return false
      }
      
      return true
    })
    
    setFilteredProducts(filtered)
  }, [filters, products])

  const handleFilterChange = (filterType: string, value: string | number | boolean) => {
    setFilters(prev => {
      if (filterType === 'priceRange') {
        return { ...prev, priceRange: value as { min: number; max: number } }
      }
      if (filterType === 'inStockOnly') {
        return { ...prev, inStockOnly: value as boolean }
      }
      
      const currentValues = prev[filterType as keyof typeof prev] as string[]
      const newValues = currentValues.includes(value as string)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value as string]
      
      return { ...prev, [filterType]: newValues }
    })
  }

  const addToCart = (product: Product, size: string, color: string, quantity: number = 1) => {
    const itemId = `${product.id}-${size}-${color}`
    const existingItem = cartItems.find(item => item.id === itemId)
    
    if (existingItem) {
      setCartItems(prev =>
        prev.map(item =>
          item.id === itemId
            ? { ...item, quantity: item.quantity + quantity, totalPrice: (item.quantity + quantity) * item.unitPrice }
            : item
        )
      )
    } else {
      const unitPrice = product.salePrice || product.basePrice
      const newItem: SolidTshirtItem = {
        id: itemId,
        productId: product.id,
        title: product.title,
        size,
        color,
        material: product.materials[0],
        brand: product.brand,
        quantity,
        unitPrice,
        totalPrice: unitPrice * quantity,
        image: product.image,
      }
      setCartItems(prev => [...prev, newItem])
    }
  }

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCartItems(prev => prev.filter(item => item.id !== itemId))
    } else {
      setCartItems(prev =>
        prev.map(item =>
          item.id === itemId
            ? { ...item, quantity: newQuantity, totalPrice: newQuantity * item.unitPrice }
            : item
        )
      )
    }
  }

  const getTotalCartValue = () => {
    return cartItems.reduce((total, item) => total + item.totalPrice, 0)
  }

  const handleAddToCartSubmit = () => {
    if (cartItems.length > 0) {
      onAddToCart(cartItems)
      setCartItems([]) // Clear cart after adding
    }
  }

  const ProductCard: React.FC<{ product: Product }> = ({ product }) => {
    const [selectedSize, setSelectedSize] = useState('')
    const [selectedColor, setSelectedColor] = useState('')
    const [quantity, setQuantity] = useState(1)

    return (
      <Card className="h-full">
        <div className="aspect-square relative overflow-hidden rounded-t-lg">
          <img
            src={product.image}
            alt={product.title}
            className="w-full h-full object-cover"
          />
          {product.salePrice && (
            <Badge className="absolute top-2 left-2 bg-red-500">
              Sale
            </Badge>
          )}
          {!product.inStock && (
            <Badge className="absolute top-2 right-2 bg-gray-500">
              Out of Stock
            </Badge>
          )}
        </div>
        
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg mb-2">{product.title}</h3>
          <p className="text-sm text-gray-600 mb-3">{product.description}</p>
          
          <div className="flex items-center gap-2 mb-3">
            {product.salePrice ? (
              <>
                <span className="text-lg font-bold text-green-600">₹{product.salePrice}</span>
                <span className="text-sm line-through text-gray-500">₹{product.basePrice}</span>
              </>
            ) : (
              <span className="text-lg font-bold">₹{product.basePrice}</span>
            )}
          </div>
          
          <div className="space-y-3">
            <div>
              <Label className="text-xs">Size</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {product.sizes.map(size => (
                  <Button
                    key={size}
                    variant={selectedSize === size ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedSize(size)}
                  >
                    {size}
                  </Button>
                ))}
              </div>
            </div>
            
            <div>
              <Label className="text-xs">Color</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {product.colors.slice(0, 5).map(color => (
                  <Button
                    key={color}
                    variant={selectedColor === color ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedColor(color)}
                  >
                    {color}
                  </Button>
                ))}
                {product.colors.length > 5 && (
                  <span className="text-xs text-gray-500 self-center">
                    +{product.colors.length - 5} more
                  </span>
                )}
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                >
                  <Minus className="h-3 w-3" />
                </Button>
                <span className="w-8 text-center">{quantity}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuantity(quantity + 1)}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              
              <Button
                onClick={() => addToCart(product, selectedSize, selectedColor, quantity)}
                disabled={!selectedSize || !selectedColor || !product.inStock}
                size="sm"
              >
                <ShoppingCart className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with view toggle and filter button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Solid T-shirts Catalog</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <div className="flex border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex gap-6">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="w-64 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Size Filter */}
                <div>
                  <Label className="font-medium">Sizes</Label>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {SIZES.map(size => (
                      <div key={size} className="flex items-center space-x-2">
                        <Checkbox
                          id={`size-${size}`}
                          checked={filters.sizes.includes(size)}
                          onCheckedChange={() => handleFilterChange('sizes', size)}
                        />
                        <Label htmlFor={`size-${size}`} className="text-sm">
                          {size}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Color Filter */}
                <div>
                  <Label className="font-medium">Colors</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2 max-h-32 overflow-y-auto">
                    {COLORS.map(color => (
                      <div key={color} className="flex items-center space-x-2">
                        <Checkbox
                          id={`color-${color}`}
                          checked={filters.colors.includes(color)}
                          onCheckedChange={() => handleFilterChange('colors', color)}
                        />
                        <Label htmlFor={`color-${color}`} className="text-sm">
                          {color}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Material Filter */}
                <div>
                  <Label className="font-medium">Materials</Label>
                  <div className="space-y-2 mt-2">
                    {MATERIALS.map(material => (
                      <div key={material} className="flex items-center space-x-2">
                        <Checkbox
                          id={`material-${material}`}
                          checked={filters.materials.includes(material)}
                          onCheckedChange={() => handleFilterChange('materials', material)}
                        />
                        <Label htmlFor={`material-${material}`} className="text-sm">
                          {material}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <Label className="font-medium">Price Range</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div>
                      <Label htmlFor="min-price" className="text-xs">Min</Label>
                      <Input
                        id="min-price"
                        type="number"
                        value={filters.priceRange.min}
                        onChange={(e) =>
                          handleFilterChange('priceRange', {
                            ...filters.priceRange,
                            min: parseInt(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="max-price" className="text-xs">Max</Label>
                      <Input
                        id="max-price"
                        type="number"
                        value={filters.priceRange.max}
                        onChange={(e) =>
                          handleFilterChange('priceRange', {
                            ...filters.priceRange,
                            max: parseInt(e.target.value) || 1000,
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* In Stock Only */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="in-stock"
                    checked={filters.inStockOnly}
                    onCheckedChange={(checked) => handleFilterChange('inStockOnly', checked)}
                  />
                  <Label htmlFor="in-stock">In Stock Only</Label>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Products Grid/List */}
        <div className="flex-1">
          <div className={`grid gap-4 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
              : 'grid-cols-1'
          }`}>
            {filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No products match your filters.</p>
            </div>
          )}
        </div>
      </div>

      {/* Cart Summary */}
      {cartItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Cart Summary ({cartItems.length} items)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {cartItems.map(item => (
                <div key={item.id} className="flex justify-between items-center py-2 border-b">
                  <div className="flex-1">
                    <p className="font-medium">{item.title}</p>
                    <p className="text-sm text-gray-600">
                      {item.size} • {item.color} • ₹{item.unitPrice}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateQuantity(item.id, item.quantity - 1)}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="w-8 text-center">{item.quantity}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                    <span className="w-20 text-right font-medium">
                      ₹{item.totalPrice}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="border-t pt-4 mt-4">
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total:</span>
                <span>₹{getTotalCartValue().toLocaleString()}</span>
              </div>
              <Button
                onClick={handleAddToCartSubmit}
                className="w-full mt-4 h-12 text-lg"
              >
                Add {cartItems.length} Items to Cart
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
