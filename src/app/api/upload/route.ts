import { NextRequest, NextResponse } from 'next/server'
import { uploadFileToS3, isS3Configured } from '@/utilities/s3'
import { writeFile, mkdir } from 'fs/promises'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const folder = formData.get('folder') as string || 'uploads'
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/svg+xml',
      'application/pdf',
      'image/webp'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only images and PDFs are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      )
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    if (isS3Configured()) {
      // Upload to S3
      try {
        const result = await uploadFileToS3(
          buffer,
          file.name,
          file.type,
          folder
        )
        
        return NextResponse.json({
          success: true,
          url: result.url,
          key: result.key,
          filename: file.name,
          size: file.size,
          type: file.type,
          storage: 's3'
        })
      } catch (error) {
        console.error('S3 upload error:', error)
        return NextResponse.json(
          { error: 'Failed to upload to S3' },
          { status: 500 }
        )
      }
    } else {
      // Fallback to local storage
      try {
        const uploadsDir = path.join(process.cwd(), 'public', 'media', folder)
        await mkdir(uploadsDir, { recursive: true })
        
        const timestamp = Date.now()
        const randomString = Math.random().toString(36).substring(2, 15)
        const extension = file.name.split('.').pop()
        const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, '')
        const sanitizedName = nameWithoutExtension.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()
        const filename = `${timestamp}-${randomString}-${sanitizedName}.${extension}`
        
        const filepath = path.join(uploadsDir, filename)
        await writeFile(filepath, buffer)
        
        const url = `/media/${folder}/${filename}`
        
        return NextResponse.json({
          success: true,
          url,
          filename,
          size: file.size,
          type: file.type,
          storage: 'local'
        })
      } catch (error) {
        console.error('Local upload error:', error)
        return NextResponse.json(
          { error: 'Failed to upload file locally' },
          { status: 500 }
        )
      }
    }
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle file deletion
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fileUrl = searchParams.get('url')
    
    if (!fileUrl) {
      return NextResponse.json(
        { error: 'No file URL provided' },
        { status: 400 }
      )
    }

    if (isS3Configured()) {
      // Delete from S3
      const { extractS3Key, deleteFromS3 } = await import('@/utilities/s3')
      const key = extractS3Key(fileUrl)
      
      if (key) {
        await deleteFromS3(key)
        return NextResponse.json({ success: true, message: 'File deleted from S3' })
      } else {
        return NextResponse.json(
          { error: 'Invalid S3 URL' },
          { status: 400 }
        )
      }
    } else {
      // Delete from local storage
      const { unlink } = await import('fs/promises')
      const filepath = path.join(process.cwd(), 'public', fileUrl)
      
      try {
        await unlink(filepath)
        return NextResponse.json({ success: true, message: 'File deleted locally' })
      } catch (error) {
        console.error('Local file deletion error:', error)
        return NextResponse.json(
          { error: 'Failed to delete local file' },
          { status: 500 }
        )
      }
    }
  } catch (error) {
    console.error('Delete error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
