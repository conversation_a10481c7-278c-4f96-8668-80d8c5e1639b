import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const featured = searchParams.get('featured')
    const search = searchParams.get('search')
    
    // Build where clause
    const where: any = {
      status: {
        equals: 'published',
      },
    }
    
    // Add featured filter if specified
    if (featured === 'true') {
      where.featured = {
        equals: true,
      }
    }
    
    // Add search filter if specified
    if (search) {
      where.or = [
        {
          name: {
            contains: search,
          },
        },
        {
          description: {
            contains: search,
          },
        },
        {
          'tags.tag': {
            contains: search,
          },
        },
      ]
    }
    
    // Fetch custom t-shirts from Payload
    const result = await payload.find({
      collection: 'custom-tshirts',
      where,
      page,
      limit,
      depth: 2, // Include related media
      sort: '-createdAt',
    })
    
    // Transform the data for frontend consumption
    const transformedProducts = result.docs.map((product: any) => ({
      id: product.id,
      name: product.name,
      description: product.description,
      basePrice: product.basePrice,
      materials: product.materials || [],
      sizes: product.sizes || [],
      colors: product.colors || [],
      styles: product.styles || [],
      images: product.images?.map((img: any) => ({
        id: img.id,
        url: typeof img.image === 'object' ? img.image.url : img.image,
        alt: img.alt || product.name,
        isPrimary: img.isPrimary || false,
      })) || [],
      printPlacements: product.printPlacements || [],
      minimumQuantity: product.minimumQuantity || 1,
      maximumQuantity: product.maximumQuantity || 1000,
      processingTime: product.processingTime || '5-7 business days',
      featured: product.featured || false,
      tags: product.tags?.map((tag: any) => tag.tag) || [],
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    }))
    
    return NextResponse.json({
      success: true,
      data: transformedProducts,
      pagination: {
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        totalDocs: result.totalDocs,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      },
    })
  } catch (error) {
    console.error('Error fetching custom t-shirts:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch custom t-shirts',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// Get single custom t-shirt by ID
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { id } = body
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Product ID is required',
        },
        { status: 400 }
      )
    }
    
    // Fetch single custom t-shirt
    const product = await payload.findByID({
      collection: 'custom-tshirts',
      id,
      depth: 2,
    })
    
    if (!product || product.status !== 'published') {
      return NextResponse.json(
        {
          success: false,
          error: 'Product not found or not published',
        },
        { status: 404 }
      )
    }
    
    // Transform the data
    const transformedProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      basePrice: product.basePrice,
      materials: product.materials || [],
      sizes: product.sizes || [],
      colors: product.colors || [],
      styles: product.styles || [],
      images: product.images?.map((img: any) => ({
        id: img.id,
        url: typeof img.image === 'object' ? img.image.url : img.image,
        alt: img.alt || product.name,
        isPrimary: img.isPrimary || false,
      })) || [],
      printPlacements: product.printPlacements || [],
      minimumQuantity: product.minimumQuantity || 1,
      maximumQuantity: product.maximumQuantity || 1000,
      processingTime: product.processingTime || '5-7 business days',
      featured: product.featured || false,
      tags: product.tags?.map((tag: any) => tag.tag) || [],
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    }
    
    return NextResponse.json({
      success: true,
      data: transformedProduct,
    })
  } catch (error) {
    console.error('Error fetching custom t-shirt:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch custom t-shirt',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
