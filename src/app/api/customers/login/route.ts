import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { email, password } = body

    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          message: 'Email and password are required',
        },
        { status: 400 }
      )
    }

    try {
      // Attempt to login the customer
      const result = await payload.login({
        collection: 'customers',
        data: {
          email,
          password,
        },
      })

      if (result.user) {
        // Check if customer account is active
        if (result.user.status !== 'active') {
          return NextResponse.json(
            {
              success: false,
              message: 'Your account is not active. Please contact support.',
            },
            { status: 403 }
          )
        }

        // Return success with user data and token
        return NextResponse.json({
          success: true,
          message: 'Login successful',
          user: {
            id: result.user.id,
            email: result.user.email,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            phone: result.user.phone,
            company: result.user.company,
            customerType: result.user.customerType,
            totalOrders: result.user.totalOrders,
            totalSpent: result.user.totalSpent,
          },
          token: result.token,
          exp: result.exp,
        })
      } else {
        return NextResponse.json(
          {
            success: false,
            message: 'Invalid email or password',
          },
          { status: 401 }
        )
      }
    } catch (loginError: any) {
      console.error('Login error:', loginError)
      
      // Handle specific login errors
      if (loginError.message?.includes('Invalid login')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Invalid email or password',
          },
          { status: 401 }
        )
      }
      
      if (loginError.message?.includes('locked')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Account temporarily locked due to too many failed attempts. Please try again later.',
          },
          { status: 423 }
        )
      }

      return NextResponse.json(
        {
          success: false,
          message: 'Login failed. Please try again.',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Customer login error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 }
    )
  }
}

// Handle logout
export async function DELETE(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Get the authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        {
          success: false,
          message: 'No authorization token provided',
        },
        { status: 401 }
      )
    }

    const token = authorization.replace('Bearer ', '')

    try {
      // Logout the customer
      await payload.logout({
        collection: 'customers',
        token,
      })

      return NextResponse.json({
        success: true,
        message: 'Logout successful',
      })
    } catch (logoutError) {
      console.error('Logout error:', logoutError)
      return NextResponse.json(
        {
          success: false,
          message: 'Logout failed',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Customer logout error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
