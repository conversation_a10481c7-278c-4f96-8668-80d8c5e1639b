import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { firstName, lastName, email, password, phone, company } = body

    // Validate required fields
    if (!firstName || !lastName || !email || !password) {
      return NextResponse.json(
        {
          success: false,
          message: 'First name, last name, email, and password are required',
        },
        { status: 400 },
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Please enter a valid email address',
        },
        { status: 400 },
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        {
          success: false,
          message: 'Password must be at least 8 characters long',
        },
        { status: 400 },
      )
    }

    // Validate phone number if provided
    if (phone && !/^[+]?[\d\s\-\(\)]{10,15}$/.test(phone)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Please enter a valid phone number',
        },
        { status: 400 },
      )
    }

    try {
      // Check if customer already exists
      const existingCustomer = await payload.find({
        collection: 'customers',
        where: {
          email: {
            equals: email.toLowerCase(),
          },
        },
        limit: 1,
      })

      if (existingCustomer.docs.length > 0) {
        return NextResponse.json(
          {
            success: false,
            message: 'An account with this email already exists',
          },
          { status: 409 },
        )
      }

      // Create new customer
      const newCustomer = await payload.create({
        collection: 'customers',
        data: {
          firstName: firstName.trim(),
          lastName: lastName.trim(),
          email: email.toLowerCase().trim(),
          password,
          phone: phone?.trim() || '**********', // Provide default if not provided
          companyName: company?.trim() || 'Individual Customer', // Required field
          customerType: company ? 'wholesale' : 'retail',
          billingAddress: {
            line1: 'To be updated',
            city: 'To be updated',
            state: 'To be updated',
            postalCode: '000000',
            country: 'India',
          },
          shippingAddress: {
            sameAsBilling: true,
          },
        },
      })

      // Return success response (without password)
      return NextResponse.json({
        success: true,
        message: 'Account created successfully',
        customer: {
          id: newCustomer.id,
          firstName: newCustomer.firstName,
          lastName: newCustomer.lastName,
          email: newCustomer.email,
          phone: newCustomer.phone,
          companyName: newCustomer.companyName,
          customerType: newCustomer.customerType,
        },
      })
    } catch (createError: any) {
      console.error('Customer creation error:', createError)

      // Handle specific creation errors
      if (createError.message?.includes('duplicate key')) {
        return NextResponse.json(
          {
            success: false,
            message: 'An account with this email already exists',
          },
          { status: 409 },
        )
      }

      if (createError.message?.includes('validation')) {
        return NextResponse.json(
          {
            success: false,
            message: 'Please check your input and try again',
          },
          { status: 400 },
        )
      }

      return NextResponse.json(
        {
          success: false,
          message: 'Failed to create account. Please try again.',
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error('Customer registration error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 },
    )
  }
}

// Get customer profile (for authenticated users)
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Get the authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        {
          success: false,
          message: 'No authorization token provided',
        },
        { status: 401 },
      )
    }

    const token = authorization.replace('Bearer ', '')

    try {
      // Verify the token and get customer data
      const customer = await payload.findByID({
        collection: 'customers',
        id: '', // This will be populated by the auth middleware
        // The token verification will happen automatically
      })

      if (!customer) {
        return NextResponse.json(
          {
            success: false,
            message: 'Customer not found',
          },
          { status: 404 },
        )
      }

      // Return customer profile (without password)
      return NextResponse.json({
        success: true,
        customer: {
          id: customer.id,
          firstName: customer.firstName,
          lastName: customer.lastName,
          email: customer.email,
          phone: customer.phone,
          company: customer.company,
          gstNumber: customer.gstNumber,
          addresses: customer.addresses,
          customerType: customer.customerType,
          totalOrders: customer.totalOrders,
          totalSpent: customer.totalSpent,
          lastOrderDate: customer.lastOrderDate,
          preferences: customer.preferences,
          status: customer.status,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt,
        },
      })
    } catch (authError) {
      console.error('Customer auth error:', authError)
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid or expired token',
        },
        { status: 401 },
      )
    }
  } catch (error) {
    console.error('Customer profile error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 },
    )
  }
}
