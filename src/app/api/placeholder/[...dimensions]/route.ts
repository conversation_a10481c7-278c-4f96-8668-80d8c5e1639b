import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ dimensions: string[] }> },
) {
  try {
    // Parse dimensions from URL
    const resolvedParams = await params
    const dimensionsStr = resolvedParams.dimensions.join('/')
    const match = dimensionsStr.match(/(\d+)x(\d+)/)

    let width = 300
    let height = 300

    if (match) {
      width = parseInt(match[1]) || 300
      height = parseInt(match[2]) || 300
    }

    // Limit dimensions for security
    width = Math.min(Math.max(width, 50), 2000)
    height = Math.min(Math.max(height, 50), 2000)

    // Generate SVG placeholder
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <rect x="20%" y="20%" width="60%" height="60%" fill="#e5e7eb" rx="8"/>
        <circle cx="40%" cy="40%" r="8%" fill="#d1d5db"/>
        <rect x="30%" y="55%" width="40%" height="4%" fill="#d1d5db" rx="2"/>
        <rect x="35%" y="65%" width="30%" height="3%" fill="#e5e7eb" rx="1"/>
        <text x="50%" y="85%" text-anchor="middle" fill="#9ca3af" font-family="Arial, sans-serif" font-size="${Math.max(12, width / 25)}">
          ${width}×${height}
        </text>
      </svg>
    `

    return new NextResponse(svg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    })
  } catch (error) {
    console.error('Placeholder image error:', error)

    // Return a simple fallback SVG
    const fallbackSvg = `
      <svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" text-anchor="middle" fill="#9ca3af" font-family="Arial, sans-serif" font-size="16">
          Image
        </text>
      </svg>
    `

    return new NextResponse(fallbackSvg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    })
  }
}
