import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)

    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const featured = searchParams.get('featured')
    const brand = searchParams.get('brand')
    const material = searchParams.get('material')
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    const search = searchParams.get('search')
    const onSale = searchParams.get('onSale')

    // Build where clause
    const where: any = {
      status: {
        equals: 'published',
      },
    }

    // Add featured filter if specified
    if (featured === 'true') {
      where.featured = {
        equals: true,
      }
    }

    // Add on sale filter if specified
    if (onSale === 'true') {
      where.onSale = {
        equals: true,
      }
    }

    // Add brand filter if specified
    if (brand) {
      where.brand = {
        equals: brand,
      }
    }

    // Add material filter if specified
    if (material) {
      where['materials.material'] = {
        equals: material,
      }
    }

    // Add price range filter if specified
    if (minPrice || maxPrice) {
      where.price = {}
      if (minPrice) {
        where.price.greater_than_equal = parseFloat(minPrice)
      }
      if (maxPrice) {
        where.price.less_than_equal = parseFloat(maxPrice)
      }
    }

    // Add search filter if specified
    if (search) {
      where.or = [
        {
          name: {
            contains: search,
          },
        },
        {
          description: {
            contains: search,
          },
        },
        {
          brand: {
            contains: search,
          },
        },
        {
          'tags.tag': {
            contains: search,
          },
        },
      ]
    }

    // Fetch solid t-shirts from Payload
    const result = await payload.find({
      collection: 'solid-tshirts',
      where,
      page,
      limit,
      depth: 2, // Include related media
      sort: '-createdAt',
    })

    // Transform the data for frontend consumption
    const transformedProducts = result.docs.map((product: any) => ({
      id: product.id,
      name: product.name,
      description: product.description,
      brand: product.brand,
      price: product.price,
      originalPrice: product.originalPrice,
      onSale: product.onSale || false,
      materials: product.materials || [],
      sizes: product.sizes || [],
      colors: product.colors || [],
      styles: product.styles || [],
      images:
        product.images?.map((img: any) => ({
          id: img.id,
          url: typeof img.image === 'object' ? img.image.url : img.image,
          alt: img.alt || product.name,
          isPrimary: img.isPrimary || false,
          colorVariant: img.colorVariant,
        })) || [],
      specifications: product.specifications || {},
      minimumQuantity: product.minimumQuantity || 1,
      maximumQuantity: product.maximumQuantity || 1000,
      rating: product.rating || 0,
      reviewCount: product.reviewCount || 0,
      featured: product.featured || false,
      tags: product.tags?.map((tag: any) => tag.tag) || [],
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    }))

    return NextResponse.json({
      success: true,
      data: transformedProducts,
      pagination: {
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        totalDocs: result.totalDocs,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      },
    })
  } catch (error) {
    console.error('Error fetching solid t-shirts:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch solid t-shirts',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

// Get single solid t-shirt by ID
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    let body
    try {
      body = await request.json()
    } catch (jsonError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid JSON in request body',
        },
        { status: 400 },
      )
    }

    const { id } = body

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Product ID is required',
        },
        { status: 400 },
      )
    }

    // Fetch single solid t-shirt
    const product = await payload.findByID({
      collection: 'solid-tshirts',
      id,
      depth: 2,
    })

    if (!product || product.status !== 'published') {
      return NextResponse.json(
        {
          success: false,
          error: 'Product not found or not published',
        },
        { status: 404 },
      )
    }

    // Transform the data
    const transformedProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      brand: product.brand,
      price: product.price,
      originalPrice: product.originalPrice,
      onSale: product.onSale || false,
      materials: product.materials || [],
      sizes: product.sizes || [],
      colors: product.colors || [],
      styles: product.styles || [],
      images:
        product.images?.map((img: any) => ({
          id: img.id,
          url: typeof img.image === 'object' ? img.image.url : img.image,
          alt: img.alt || product.name,
          isPrimary: img.isPrimary || false,
          colorVariant: img.colorVariant,
        })) || [],
      specifications: product.specifications || {},
      minimumQuantity: product.minimumQuantity || 1,
      maximumQuantity: product.maximumQuantity || 1000,
      rating: product.rating || 0,
      reviewCount: product.reviewCount || 0,
      featured: product.featured || false,
      tags: product.tags?.map((tag: any) => tag.tag) || [],
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedProduct,
    })
  } catch (error) {
    console.error('Error fetching solid t-shirt:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch solid t-shirt',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
