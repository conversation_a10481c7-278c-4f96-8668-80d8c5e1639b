import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'
import { anyone } from '../access/anyone'

export const SolidTshirts: CollectionConfig = {
  slug: 'solid-tshirts',
  labels: {
    singular: 'Solid T-shirt',
    plural: 'Solid T-shirts',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'price', 'brand', 'status', 'updatedAt'],
    group: 'Products',
  },
  access: {
    read: anyone,
    create: authenticated,
    update: authenticated,
    delete: authenticated,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Product Name',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
    },
    {
      name: 'brand',
      type: 'select',
      required: true,
      label: 'Brand',
      options: [
        { label: 'Maddox', value: 'maddox' },
        { label: 'Comfort', value: 'comfort' },
        { label: 'Premium', value: 'premium' },
        { label: 'Basic', value: 'basic' },
        { label: 'Eco-Friendly', value: 'eco-friendly' },
      ],
    },
    {
      name: 'price',
      type: 'number',
      required: true,
      label: 'Price (₹)',
      min: 0,
    },
    {
      name: 'originalPrice',
      type: 'number',
      label: 'Original Price (₹)',
      admin: {
        description: 'If this product is on sale, enter the original price here',
      },
    },
    {
      name: 'materials',
      type: 'array',
      label: 'Available Materials',
      minRows: 1,
      fields: [
        {
          name: 'material',
          type: 'select',
          required: true,
          options: [
            { label: 'Cotton', value: 'cotton' },
            { label: 'Polyester', value: 'polyester' },
            { label: 'Poly-Cotton Blend', value: 'poly-cotton' },
            { label: 'Organic Cotton', value: 'organic-cotton' },
            { label: 'Bamboo', value: 'bamboo' },
            { label: 'Modal', value: 'modal' },
          ],
        },
        {
          name: 'percentage',
          type: 'number',
          label: 'Percentage (%)',
          min: 0,
          max: 100,
        },
      ],
    },
    {
      name: 'sizes',
      type: 'array',
      label: 'Available Sizes',
      minRows: 1,
      fields: [
        {
          name: 'size',
          type: 'select',
          required: true,
          options: [
            { label: 'XS', value: 'xs' },
            { label: 'S', value: 's' },
            { label: 'M', value: 'm' },
            { label: 'L', value: 'l' },
            { label: 'XL', value: 'xl' },
            { label: 'XXL', value: 'xxl' },
            { label: 'XXXL', value: 'xxxl' },
          ],
        },
        {
          name: 'stock',
          type: 'number',
          label: 'Stock Quantity',
          defaultValue: 0,
          min: 0,
        },
      ],
    },
    {
      name: 'colors',
      type: 'array',
      label: 'Available Colors',
      minRows: 1,
      fields: [
        {
          name: 'colorName',
          type: 'text',
          required: true,
          label: 'Color Name',
        },
        {
          name: 'colorCode',
          type: 'text',
          label: 'Color Code (Hex)',
          validate: (val: string | null | undefined) => {
            if (val && !/^#[0-9A-F]{6}$/i.test(val)) {
              return 'Please enter a valid hex color code (e.g., #FF0000)'
            }
            return true
          },
        },
        {
          name: 'stock',
          type: 'number',
          label: 'Stock Quantity',
          defaultValue: 0,
          min: 0,
        },
      ],
    },
    {
      name: 'styles',
      type: 'array',
      label: 'Available Styles',
      minRows: 1,
      fields: [
        {
          name: 'styleName',
          type: 'select',
          required: true,
          options: [
            { label: 'Round Neck', value: 'round-neck' },
            { label: 'V-Neck', value: 'v-neck' },
            { label: 'Polo', value: 'polo' },
            { label: 'Hoodie', value: 'hoodie' },
            { label: 'Tank Top', value: 'tank-top' },
            { label: 'Long Sleeve', value: 'long-sleeve' },
          ],
        },
        {
          name: 'stock',
          type: 'number',
          label: 'Stock Quantity',
          defaultValue: 0,
          min: 0,
        },
      ],
    },
    {
      name: 'images',
      type: 'array',
      label: 'Product Images',
      minRows: 1,
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'alt',
          type: 'text',
          label: 'Alt Text',
        },
        {
          name: 'isPrimary',
          type: 'checkbox',
          label: 'Primary Image',
          defaultValue: false,
        },
        {
          name: 'colorVariant',
          type: 'text',
          label: 'Color Variant',
          admin: {
            description: 'Which color this image represents',
          },
        },
      ],
    },
    {
      name: 'specifications',
      type: 'group',
      label: 'Specifications',
      fields: [
        {
          name: 'weight',
          type: 'text',
          label: 'Weight (GSM)',
        },
        {
          name: 'fit',
          type: 'select',
          label: 'Fit',
          options: [
            { label: 'Regular Fit', value: 'regular' },
            { label: 'Slim Fit', value: 'slim' },
            { label: 'Loose Fit', value: 'loose' },
            { label: 'Oversized', value: 'oversized' },
          ],
        },
        {
          name: 'neckType',
          type: 'select',
          label: 'Neck Type',
          options: [
            { label: 'Round Neck', value: 'round' },
            { label: 'V-Neck', value: 'v-neck' },
            { label: 'Collar', value: 'collar' },
            { label: 'Hooded', value: 'hooded' },
          ],
        },
        {
          name: 'sleeveLength',
          type: 'select',
          label: 'Sleeve Length',
          options: [
            { label: 'Short Sleeve', value: 'short' },
            { label: 'Long Sleeve', value: 'long' },
            { label: 'Sleeveless', value: 'sleeveless' },
            { label: '3/4 Sleeve', value: 'three-quarter' },
          ],
        },
      ],
    },
    {
      name: 'minimumQuantity',
      type: 'number',
      label: 'Minimum Order Quantity',
      defaultValue: 1,
      min: 1,
    },
    {
      name: 'maximumQuantity',
      type: 'number',
      label: 'Maximum Order Quantity',
      defaultValue: 1000,
      min: 1,
    },
    {
      name: 'rating',
      type: 'number',
      label: 'Average Rating',
      min: 0,
      max: 5,
      admin: {
        step: 0.1,
        description: 'Average customer rating (0-5)',
      },
    },
    {
      name: 'reviewCount',
      type: 'number',
      label: 'Number of Reviews',
      defaultValue: 0,
      min: 0,
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
        { label: 'Out of Stock', value: 'out-of-stock' },
        { label: 'Archived', value: 'archived' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      label: 'Featured Product',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'onSale',
      type: 'checkbox',
      label: 'On Sale',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Ensure only one primary image
        if (data.images && Array.isArray(data.images)) {
          let primaryCount = 0
          data.images.forEach((img: any, index: number) => {
            if (img.isPrimary) {
              primaryCount++
              if (primaryCount > 1) {
                data.images[index].isPrimary = false
              }
            }
          })

          // If no primary image, make the first one primary
          if (primaryCount === 0 && data.images.length > 0) {
            data.images[0].isPrimary = true
          }
        }

        return data
      },
    ],
  },
}
