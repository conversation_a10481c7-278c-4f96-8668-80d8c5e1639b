'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type ServiceType = 'dtf-stickers' | 'solid-tshirts' | 'custom-tshirts'

export type CartItem = {
  id: string
  serviceType: ServiceType
  title: string
  price: number
  quantity: number
  summary: string
  image?: string

  // Service-specific data
  serviceData: {
    // DTF Stickers
    dtfStickers?: {
      designFile: string // File URL after upload
      dimensions: { width: number; height: number }
      material: string
      finish: string
      pricePerUnit: number
    }

    // Solid T-shirts
    solidTshirts?: Array<{
      productId: string
      size: string
      color: string
      material: string
      brand: string
      unitPrice: number
    }>

    // Custom T-shirts
    customTshirts?: {
      tshirtSelection: {
        baseProduct: string
        size: string
        color: string
        material: string
        style: string
      }
      designFile: string // File URL after upload
      mockupImage?: string
      printPlacement: string
      printSize: { width: number; height: number }
      advanceAmount: number
      remainingAmount: number
      totalPrice: number
    }
  }

  // Payment terms
  paymentTerms: 'full_upfront' | 'split_payment'
  advanceAmount?: number // For split payments
  remainingAmount?: number // For split payments
}

type CartStore = {
  items: CartItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  totalAdvanceAmount: number // Total advance payment required
  totalRemainingAmount: number // Total remaining payment

  // Actions
  addItem: (item: CartItem) => void
  updateItem: (id: string, updates: Partial<CartItem>) => void
  removeItem: (id: string) => void
  clearCart: () => void
  calculateTotals: () => void
  setShipping: (amount: number) => void
  setDiscount: (amount: number) => void

  // Service-specific helpers
  addDTFStickers: (data: any) => void
  addSolidTshirts: (data: any[]) => void
  addCustomTshirts: (data: any) => void

  // Getters
  getItemsByService: (serviceType: ServiceType) => CartItem[]
  getTotalItemCount: () => number
  hasItemsRequiringSplitPayment: () => boolean
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      subtotal: 0,
      tax: 0,
      shipping: 0,
      discount: 0,
      total: 0,
      totalAdvanceAmount: 0,
      totalRemainingAmount: 0,

      addItem: (item) => {
        const { items } = get()
        // For the new service-based structure, each item is unique
        // No need to check for duplicates as each service has different data
        set({ items: [...items, { ...item, id: crypto.randomUUID() }] })
        get().calculateTotals()
      },

      updateItem: (id, updates) => {
        const { items } = get()
        const updatedItems = items.map((item) => (item.id === id ? { ...item, ...updates } : item))
        set({ items: updatedItems })
        get().calculateTotals()
      },

      removeItem: (id) => {
        const { items } = get()
        const updatedItems = items.filter((item) => item.id !== id)
        set({ items: updatedItems })
        get().calculateTotals()
      },

      clearCart: () => {
        set({
          items: [],
          subtotal: 0,
          tax: 0,
          shipping: 0,
          discount: 0,
          total: 0,
          totalAdvanceAmount: 0,
          totalRemainingAmount: 0,
        })
      },

      calculateTotals: () => {
        const { items, shipping, discount } = get()

        // Calculate subtotal from item prices
        const subtotal = items.reduce((sum, item) => sum + item.price, 0)

        // Calculate tax (18% GST)
        const tax = subtotal * 0.18

        // Calculate total
        const total = subtotal + tax + shipping - discount

        // Calculate advance and remaining amounts for split payments
        const totalAdvanceAmount = items
          .filter((item) => item.paymentTerms === 'split_payment')
          .reduce((sum, item) => sum + (item.advanceAmount || 0), 0)

        const totalRemainingAmount = items
          .filter((item) => item.paymentTerms === 'split_payment')
          .reduce((sum, item) => sum + (item.remainingAmount || 0), 0)

        set({ subtotal, tax, total, totalAdvanceAmount, totalRemainingAmount })
      },

      setShipping: (amount) => {
        set({ shipping: amount })
        get().calculateTotals()
      },

      setDiscount: (amount) => {
        set({ discount: amount })
        get().calculateTotals()
      },

      // Service-specific helpers
      addDTFStickers: (data) => {
        const item: CartItem = {
          id: crypto.randomUUID(),
          serviceType: 'dtf-stickers',
          title: `DTF Stickers - ${data.quantity}m`,
          price: data.totalPrice,
          quantity: 1,
          summary: `${data.quantity}m, ${data.material}, ${data.finish}`,
          serviceData: {
            dtfStickers: {
              designFile: '', // Will be set after file upload
              dimensions: data.dimensions,
              material: data.material,
              finish: data.finish,
              pricePerUnit: data.pricePerUnit,
            },
          },
          paymentTerms: 'full_upfront',
        }
        get().addItem(item)
      },

      addSolidTshirts: (data) => {
        const totalPrice = data.reduce((sum: number, item: any) => sum + item.totalPrice, 0)
        const item: CartItem = {
          id: crypto.randomUUID(),
          serviceType: 'solid-tshirts',
          title: `${data.length} Solid T-shirt${data.length > 1 ? 's' : ''}`,
          price: totalPrice,
          quantity: 1,
          summary: `${data.length} items`,
          serviceData: {
            solidTshirts: data.map((tshirt: any) => ({
              productId: tshirt.productId,
              size: tshirt.size,
              color: tshirt.color,
              material: tshirt.material,
              brand: tshirt.brand,
              unitPrice: tshirt.unitPrice,
            })),
          },
          paymentTerms: 'full_upfront',
        }
        get().addItem(item)
      },

      addCustomTshirts: (data) => {
        const item: CartItem = {
          id: crypto.randomUUID(),
          serviceType: 'custom-tshirts',
          title: `Custom T-shirt - ${data.quantity} pcs`,
          price: data.totalPrice,
          quantity: 1,
          summary: `${data.quantity} pcs, ${data.printPlacement} print`,
          serviceData: {
            customTshirts: {
              tshirtSelection: data.tshirtSelection,
              designFile: '', // Will be set after file upload
              mockupImage: data.mockupImage,
              printPlacement: data.printPlacement,
              printSize: data.printSize,
              advanceAmount: data.advanceAmount,
              remainingAmount: data.remainingAmount,
              totalPrice: data.totalPrice,
            },
          },
          paymentTerms: 'split_payment',
          advanceAmount: data.advanceAmount,
          remainingAmount: data.remainingAmount,
        }
        get().addItem(item)
      },

      // Getters
      getItemsByService: (serviceType) => {
        return get().items.filter((item) => item.serviceType === serviceType)
      },

      getTotalItemCount: () => {
        return get().items.length
      },

      hasItemsRequiringSplitPayment: () => {
        return get().items.some((item) => item.paymentTerms === 'split_payment')
      },
    }),
    {
      name: 'maddox-tees-cart',
    },
  ),
)
