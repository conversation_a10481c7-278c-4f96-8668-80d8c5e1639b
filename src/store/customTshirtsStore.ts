import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface CustomTshirtData {
  tshirtSelection: {
    baseProduct: string
    size: string
    color: string
    material: string
    style: string
  }
  designFile: File | null
  uploadedFileUrl?: string
  uploadedFileKey?: string
  mockupImage?: string
  mockupImageUrl?: string
  printPlacement: string
  printSize: {
    width: number
    height: number
  }
  quantity: number
  advanceAmount: number
  remainingAmount: number
  totalPrice: number
}

export interface BaseTshirt {
  id: string
  name: string
  basePrice: number
  image: string
  colors: string[]
  materials: string[]
  styles: string[]
}

interface CustomTshirtsState {
  currentStep: 'tshirt' | 'design'
  formData: CustomTshirtData
  baseTshirts: BaseTshirt[]
  previewUrl: string | null
  mockupUrl: string | null
  isGeneratingMockup: boolean
  zoom: number
  isCustomPrintSize: boolean

  // Validation states
  isTshirtStepValid: boolean
  isDesignStepValid: boolean

  // Actions
  setCurrentStep: (step: 'tshirt' | 'design') => void
  setTshirtSelection: (selection: Partial<CustomTshirtData['tshirtSelection']>) => void
  setDesignFile: (file: File | null) => void
  setPrintPlacement: (placement: string) => void
  setPrintSize: (size: Partial<CustomTshirtData['printSize']>) => void
  setQuantity: (quantity: number) => void
  setPreviewUrl: (url: string | null) => void
  setMockupUrl: (url: string | null) => void
  setIsGeneratingMockup: (generating: boolean) => void
  setZoom: (zoom: number) => void
  setIsCustomPrintSize: (isCustom: boolean) => void
  setBaseTshirts: (tshirts: BaseTshirt[]) => void

  // Business logic
  calculatePricing: () => void
  validateTshirtStep: () => boolean
  validateDesignStep: () => boolean
  generateMockup: () => Promise<void>
  resetForm: () => void
  getSelectedTshirt: () => BaseTshirt | undefined
}

const initialFormData: CustomTshirtData = {
  tshirtSelection: {
    baseProduct: '',
    size: '',
    color: '',
    material: '',
    style: '',
  },
  designFile: null,
  printPlacement: '',
  printSize: { width: 0, height: 0 },
  quantity: 1,
  advanceAmount: 0,
  remainingAmount: 0,
  totalPrice: 0,
}

export const useCustomTshirtsStore = create<CustomTshirtsState>()(
  persist(
    (set, get) => ({
      currentStep: 'tshirt',
      formData: initialFormData,
      baseTshirts: [],
      previewUrl: null,
      mockupUrl: null,
      isGeneratingMockup: false,
      zoom: 1,
      isCustomPrintSize: false,
      isTshirtStepValid: false,
      isDesignStepValid: false,

      setCurrentStep: (step) => {
        set({ currentStep: step })
      },

      setTshirtSelection: (selection) => {
        set((state) => ({
          formData: {
            ...state.formData,
            tshirtSelection: { ...state.formData.tshirtSelection, ...selection },
          },
        }))
        get().validateTshirtStep()
        get().calculatePricing()
      },

      setDesignFile: (file) => {
        set((state) => ({
          formData: { ...state.formData, designFile: file },
        }))
        get().validateDesignStep()

        if (file) {
          get().generateMockup()
        }
      },

      setPrintPlacement: (placement) => {
        set((state) => ({
          formData: { ...state.formData, printPlacement: placement },
        }))
        get().validateDesignStep()
      },

      setPrintSize: (size) => {
        set((state) => ({
          formData: {
            ...state.formData,
            printSize: { ...state.formData.printSize, ...size },
          },
        }))
        get().validateDesignStep()
      },

      setQuantity: (quantity) => {
        set((state) => ({
          formData: { ...state.formData, quantity },
        }))
        get().calculatePricing()
      },

      setPreviewUrl: (url) => {
        set({ previewUrl: url })
      },

      setMockupUrl: (url) => {
        set({ mockupUrl: url })
      },

      setIsGeneratingMockup: (generating) => {
        set({ isGeneratingMockup: generating })
      },

      setZoom: (zoom) => {
        set({ zoom: Math.max(0.5, Math.min(3, zoom)) })
      },

      setIsCustomPrintSize: (isCustom) => {
        set({ isCustomPrintSize: isCustom })
        if (!isCustom) {
          // Reset to default size when switching away from custom
          set((state) => ({
            formData: {
              ...state.formData,
              printSize: { width: 15, height: 15 }, // Default medium size
            },
          }))
        }
      },

      setBaseTshirts: (tshirts) => {
        set({ baseTshirts: tshirts })
      },

      calculatePricing: () => {
        const { formData, baseTshirts } = get()
        const selectedTshirt = baseTshirts.find(
          (t) => t.id === formData.tshirtSelection.baseProduct,
        )

        if (!selectedTshirt) return

        const basePrice = selectedTshirt.basePrice
        const printingCost = 150 // Base printing cost per unit
        const totalPerUnit = basePrice + printingCost
        const totalPrice = totalPerUnit * formData.quantity
        const advanceAmount = Math.round(totalPrice * 0.5) // 50% advance
        const remainingAmount = totalPrice - advanceAmount

        set((state) => ({
          formData: {
            ...state.formData,
            advanceAmount,
            remainingAmount,
            totalPrice,
          },
        }))
      },

      validateTshirtStep: () => {
        const { baseProduct, size, color, material, style } = get().formData.tshirtSelection
        const isValid = !!(baseProduct && size && color && material && style)

        set({ isTshirtStepValid: isValid })
        return isValid
      },

      validateDesignStep: () => {
        const { formData } = get()
        const isValid = !!(
          formData.designFile &&
          formData.printPlacement &&
          formData.printSize.width > 0 &&
          formData.printSize.height > 0 &&
          formData.quantity > 0
        )

        set({ isDesignStepValid: isValid })
        return isValid
      },

      generateMockup: async () => {
        const { formData } = get()

        if (!formData.designFile || !formData.tshirtSelection.baseProduct) return

        set({ isGeneratingMockup: true })

        try {
          // Mock mockup generation - in real implementation, this would call an API
          // that overlays the design on the selected t-shirt
          await new Promise((resolve) => setTimeout(resolve, 1000))

          // For now, just use a placeholder mockup URL
          set({ mockupUrl: '/api/placeholder/400/400' })
        } catch (error) {
          console.error('Failed to generate mockup:', error)
        } finally {
          set({ isGeneratingMockup: false })
        }
      },

      resetForm: () => {
        set({
          currentStep: 'tshirt',
          formData: initialFormData,
          previewUrl: null,
          mockupUrl: null,
          isGeneratingMockup: false,
          zoom: 1,
          isCustomPrintSize: false,
          isTshirtStepValid: false,
          isDesignStepValid: false,
        })
      },

      getSelectedTshirt: () => {
        const { formData, baseTshirts } = get()
        return baseTshirts.find((t) => t.id === formData.tshirtSelection.baseProduct)
      },
    }),
    {
      name: 'custom-tshirts-store',
      // Only persist non-file data
      partialize: (state) => ({
        currentStep: state.currentStep,
        formData: {
          ...state.formData,
          designFile: null, // Don't persist files
        },
        zoom: state.zoom,
        isCustomPrintSize: state.isCustomPrintSize,
        // Reset validation states on reload
        isTshirtStepValid: false,
        isDesignStepValid: false,
      }),
    },
  ),
)
