import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface DTFStickerData {
  designFile: File | null
  dimensions: {
    width: number
    height: number
    isCustomSize: boolean
  }
  material: string
  finish: string
  quantity: number
  pricePerUnit: number
  totalPrice: number
}

interface DTFStickersState {
  formData: DTFStickerData
  previewUrl: string | null
  isValid: boolean
  
  // Actions
  setDesignFile: (file: File | null) => void
  setDimensions: (dimensions: Partial<DTFStickerData['dimensions']>) => void
  setMaterial: (material: string) => void
  setFinish: (finish: string) => void
  setQuantity: (quantity: number) => void
  setPreviewUrl: (url: string | null) => void
  calculatePrice: () => void
  validateForm: () => boolean
  resetForm: () => void
}

const initialFormData: DTFStickerData = {
  designFile: null,
  dimensions: { width: 0, height: 0, isCustomSize: false },
  material: '',
  finish: '',
  quantity: 1,
  pricePerUnit: 0,
  totalPrice: 0,
}

// Calculate price based on quantity tiers
const calculatePricePerUnit = (quantity: number): number => {
  if (quantity <= 5) return 250
  if (quantity <= 10) return 240
  return 220
}

export const useDTFStickersStore = create<DTFStickersState>()(
  persist(
    (set, get) => ({
      formData: initialFormData,
      previewUrl: null,
      isValid: false,

      setDesignFile: (file) => {
        set((state) => ({
          formData: { ...state.formData, designFile: file },
        }))
        get().validateForm()
      },

      setDimensions: (dimensions) => {
        set((state) => ({
          formData: {
            ...state.formData,
            dimensions: { ...state.formData.dimensions, ...dimensions },
          },
        }))
        get().validateForm()
      },

      setMaterial: (material) => {
        set((state) => ({
          formData: { ...state.formData, material },
        }))
        get().validateForm()
      },

      setFinish: (finish) => {
        set((state) => ({
          formData: { ...state.formData, finish },
        }))
        get().validateForm()
      },

      setQuantity: (quantity) => {
        set((state) => ({
          formData: { ...state.formData, quantity },
        }))
        get().calculatePrice()
        get().validateForm()
      },

      setPreviewUrl: (url) => {
        set({ previewUrl: url })
      },

      calculatePrice: () => {
        const { quantity } = get().formData
        const pricePerUnit = calculatePricePerUnit(quantity)
        const totalPrice = pricePerUnit * quantity

        set((state) => ({
          formData: {
            ...state.formData,
            pricePerUnit,
            totalPrice,
          },
        }))
      },

      validateForm: () => {
        const { formData } = get()
        const isValid = !!(
          formData.designFile &&
          formData.dimensions.width > 0 &&
          formData.dimensions.height > 0 &&
          formData.material &&
          formData.finish &&
          formData.quantity > 0
        )

        set({ isValid })
        return isValid
      },

      resetForm: () => {
        set({
          formData: initialFormData,
          previewUrl: null,
          isValid: false,
        })
      },
    }),
    {
      name: 'dtf-stickers-store',
      // Only persist non-file data
      partialize: (state) => ({
        formData: {
          ...state.formData,
          designFile: null, // Don't persist files
        },
        isValid: false, // Reset validation on reload
      }),
    }
  )
)
