import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface SolidTshirtItem {
  id: string
  productId: string
  title: string
  size: string
  color: string
  material: string
  brand: string
  quantity: number
  unitPrice: number
  totalPrice: number
  image?: string
}

export interface Product {
  id: string
  title: string
  description: string
  basePrice: number
  salePrice?: number
  image: string
  sizes: string[]
  colors: string[]
  materials: string[]
  brand: string
  inStock: boolean
  category: string
}

export interface Filters {
  sizes: string[]
  colors: string[]
  materials: string[]
  brands: string[]
  priceRange: { min: number; max: number }
  inStockOnly: boolean
}

interface SolidTshirtsState {
  products: Product[]
  filteredProducts: Product[]
  cartItems: SolidTshirtItem[]
  filters: Filters
  viewMode: 'grid' | 'list'
  showFilters: boolean

  // Actions
  setProducts: (products: Product[]) => void
  setFilters: (filters: Partial<Filters>) => void
  setViewMode: (mode: 'grid' | 'list') => void
  setShowFilters: (show: boolean) => void
  addToCart: (product: Product, size: string, color: string, quantity?: number) => void
  updateQuantity: (itemId: string, quantity: number) => void
  removeFromCart: (itemId: string) => void
  clearCart: () => void
  applyFilters: () => void
  getTotalCartValue: () => number
  getCartItemCount: () => number
}

const initialFilters: Filters = {
  sizes: [],
  colors: [],
  materials: [],
  brands: [],
  priceRange: { min: 0, max: 1000 },
  inStockOnly: false,
}

export const useSolidTshirtsStore = create<SolidTshirtsState>()(
  persist(
    (set, get) => ({
      products: [],
      filteredProducts: [],
      cartItems: [],
      filters: initialFilters,
      viewMode: 'grid',
      showFilters: false,

      setProducts: (products) => {
        set({ products, filteredProducts: products })
        get().applyFilters()
      },

      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
        }))
        get().applyFilters()
      },

      setViewMode: (mode) => {
        set({ viewMode: mode })
      },

      setShowFilters: (show) => {
        set({ showFilters: show })
      },

      addToCart: (product, size, color, quantity = 1) => {
        const itemId = `${product.id}-${size}-${color}`
        const existingItem = get().cartItems.find((item) => item.id === itemId)

        if (existingItem) {
          get().updateQuantity(itemId, existingItem.quantity + quantity)
        } else {
          const unitPrice = product.salePrice || product.basePrice
          const newItem: SolidTshirtItem = {
            id: itemId,
            productId: product.id,
            title: product.title,
            size,
            color,
            material: product.materials[0] || 'Cotton',
            brand: product.brand,
            quantity,
            unitPrice,
            totalPrice: unitPrice * quantity,
            image: product.image,
          }

          set((state) => ({
            cartItems: [...state.cartItems, newItem],
          }))
        }
      },

      updateQuantity: (itemId, newQuantity) => {
        if (newQuantity <= 0) {
          get().removeFromCart(itemId)
        } else {
          set((state) => ({
            cartItems: state.cartItems.map((item) =>
              item.id === itemId
                ? { ...item, quantity: newQuantity, totalPrice: newQuantity * item.unitPrice }
                : item,
            ),
          }))
        }
      },

      removeFromCart: (itemId) => {
        set((state) => ({
          cartItems: state.cartItems.filter((item) => item.id !== itemId),
        }))
      },

      clearCart: () => {
        set({ cartItems: [] })
      },

      applyFilters: () => {
        const { products, filters } = get()

        const filtered = products.filter((product) => {
          // Size filter
          if (filters.sizes.length > 0) {
            const hasMatchingSize = filters.sizes.some((size) => product.sizes.includes(size))
            if (!hasMatchingSize) return false
          }

          // Color filter
          if (filters.colors.length > 0) {
            const hasMatchingColor = filters.colors.some((color) => product.colors.includes(color))
            if (!hasMatchingColor) return false
          }

          // Material filter
          if (filters.materials.length > 0) {
            const hasMatchingMaterial = filters.materials.some((material) =>
              product.materials.includes(material),
            )
            if (!hasMatchingMaterial) return false
          }

          // Brand filter
          if (filters.brands.length > 0 && !filters.brands.includes(product.brand)) {
            return false
          }

          // Price filter
          const price = product.salePrice || product.basePrice
          if (price < filters.priceRange.min || price > filters.priceRange.max) {
            return false
          }

          // Stock filter
          if (filters.inStockOnly && !product.inStock) {
            return false
          }

          return true
        })

        set({ filteredProducts: filtered })
      },

      getTotalCartValue: () => {
        return get().cartItems.reduce((total, item) => total + item.totalPrice, 0)
      },

      getCartItemCount: () => {
        return get().cartItems.reduce((total, item) => total + item.quantity, 0)
      },
    }),
    {
      name: 'solid-tshirts-store',
      partialize: (state) => ({
        cartItems: state.cartItems,
        filters: state.filters,
        viewMode: state.viewMode,
        showFilters: state.showFilters,
      }),
    },
  ),
)
