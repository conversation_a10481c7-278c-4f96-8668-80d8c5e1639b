import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

// S3 client configuration
const createS3Client = () => {
  const config: any = {
    region: process.env.S3_REGION!,
  }

  // Only specify explicit credentials if they are provided
  // Otherwise, let AWS SDK handle credentials automatically (profile, IAM roles, etc.)
  if (process.env.S3_ACCESS_KEY_ID && process.env.S3_SECRET_ACCESS_KEY) {
    config.credentials = {
      accessKeyId: process.env.S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
    }
  }
  // If AWS_PROFILE is set, the SDK will automatically use it from ~/.aws/credentials

  // Custom endpoint for S3-compatible services
  if (process.env.S3_ENDPOINT) {
    config.endpoint = process.env.S3_ENDPOINT
    config.forcePathStyle = true
  }

  return new S3Client(config)
}

export const s3Client = createS3Client()

// Upload file to S3
export const uploadToS3 = async (
  file: Buffer | Uint8Array | string,
  key: string,
  contentType?: string,
  metadata?: Record<string, string>,
): Promise<string> => {
  try {
    const command = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET!,
      Key: key,
      Body: file,
      ContentType: contentType,
      Metadata: metadata,
    })

    await s3Client.send(command)

    // Return the S3 URL
    const baseUrl = process.env.S3_ENDPOINT
      ? `${process.env.S3_ENDPOINT}/${process.env.S3_BUCKET}`
      : `https://${process.env.S3_BUCKET}.s3.${process.env.S3_REGION}.amazonaws.com`

    return `${baseUrl}/${key}`
  } catch (error) {
    console.error('Error uploading to S3:', error)
    throw new Error(`Failed to upload file to S3: ${error}`)
  }
}

// Delete file from S3
export const deleteFromS3 = async (key: string): Promise<void> => {
  try {
    const command = new DeleteObjectCommand({
      Bucket: process.env.S3_BUCKET!,
      Key: key,
    })

    await s3Client.send(command)
  } catch (error) {
    console.error('Error deleting from S3:', error)
    throw new Error(`Failed to delete file from S3: ${error}`)
  }
}

// Generate presigned URL for secure file access
export const generatePresignedUrl = async (
  key: string,
  expiresIn: number = 3600, // 1 hour default
): Promise<string> => {
  try {
    const command = new GetObjectCommand({
      Bucket: process.env.S3_BUCKET!,
      Key: key,
    })

    return await getSignedUrl(s3Client, command, { expiresIn })
  } catch (error) {
    console.error('Error generating presigned URL:', error)
    throw new Error(`Failed to generate presigned URL: ${error}`)
  }
}

// Generate unique file key with timestamp and random string
export const generateFileKey = (originalName: string, folder: string = 'uploads'): string => {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const extension = originalName.split('.').pop()
  const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, '')
  const sanitizedName = nameWithoutExtension.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()

  return `${folder}/${timestamp}-${randomString}-${sanitizedName}.${extension}`
}

// Check if S3 is configured
export const isS3Configured = (): boolean => {
  return !!(process.env.S3_BUCKET && process.env.S3_REGION)
  // AWS credentials can be provided via:
  // 1. AWS_PROFILE environment variable (uses ~/.aws/credentials)
  // 2. S3_ACCESS_KEY_ID and S3_SECRET_ACCESS_KEY environment variables
  // 3. IAM roles (for EC2/Lambda)
  // 4. AWS CLI default profile
  // The AWS SDK will automatically detect and use available credentials
}

// Get file URL (S3 or local based on configuration)
export const getFileUrl = (filename: string): string => {
  if (isS3Configured()) {
    const baseUrl = process.env.S3_ENDPOINT
      ? `${process.env.S3_ENDPOINT}/${process.env.S3_BUCKET}`
      : `https://${process.env.S3_BUCKET}.s3.${process.env.S3_REGION}.amazonaws.com`

    return `${baseUrl}/${filename}`
  } else {
    // Return local URL
    return `/media/${filename}`
  }
}

// Extract S3 key from URL
export const extractS3Key = (url: string): string | null => {
  try {
    if (!url) return null

    // Handle S3 URLs
    if (url.includes('amazonaws.com') || url.includes('s3.')) {
      const urlParts = url.split('/')
      return urlParts.slice(3).join('/') // Remove protocol, domain, and bucket
    }

    // Handle custom endpoint URLs
    if (process.env.S3_ENDPOINT && url.includes(process.env.S3_ENDPOINT)) {
      const urlParts = url.split('/')
      const bucketIndex = urlParts.findIndex((part) => part === process.env.S3_BUCKET)
      if (bucketIndex !== -1) {
        return urlParts.slice(bucketIndex + 1).join('/')
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting S3 key:', error)
    return null
  }
}

// Upload file with automatic key generation
export const uploadFileToS3 = async (
  file: Buffer | Uint8Array,
  originalName: string,
  contentType: string,
  folder: string = 'uploads',
): Promise<{ url: string; key: string }> => {
  const key = generateFileKey(originalName, folder)
  const url = await uploadToS3(file, key, contentType)

  return { url, key }
}

// Batch delete files from S3
export const batchDeleteFromS3 = async (keys: string[]): Promise<void> => {
  const deletePromises = keys.map((key) => deleteFromS3(key))
  await Promise.all(deletePromises)
}
